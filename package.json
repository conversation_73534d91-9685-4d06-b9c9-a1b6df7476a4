{"name": "redsoft-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@floating-ui/react": "^0.27.15", "@floating-ui/react-dom": "^2.1.5", "@tanstack/react-query": "^5.81.5", "@types/react-window": "^1.8.8", "axios": "^1.10.0", "bootstrap": "^5.3.5", "date-fns": "^4.1.0", "formik": "^2.4.6", "html-react-parser": "^5.2.6", "i18next": "^25.3.2", "iconsax-react": "^0.0.8", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-chat-elements": "^12.0.18", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-otp-input": "^3.1.1", "react-router-dom": "^7.5.0", "react-select": "^5.10.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "socket.io-client": "^4.8.1", "yup": "^1.6.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/google.maps": "^3.58.1", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.6.2", "sass": "^1.86.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}