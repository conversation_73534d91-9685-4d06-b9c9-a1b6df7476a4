import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import auth from '@/locales/auth.json';
import common from '@/locales/common.json';
import pages from '@/locales/pages.json';
import components from '@/locales/components.json';
import deAuth from '@/locales/de/auth.json';
import deCommon from '@/locales/de/common.json';
import dePages from '@/locales/de/pages.json';
import deComponents from '@/locales/de/components.json';
import daAuth from '@/locales/da/auth.json';
import daCommon from '@/locales/da/common.json';
import daPages from '@/locales/da/pages.json';
import daComponents from '@/locales/da/components.json';
import noAuth from '@/locales/no/auth.json';
import noCommon from '@/locales/no/common.json';
import noPages from '@/locales/no/pages.json';
import noComponents from '@/locales/no/components.json';
import svAuth from '@/locales/sv/auth.json';
import svCommon from '@/locales/sv/common.json';
import svPages from '@/locales/sv/pages.json';
import svComponents from '@/locales/sv/components.json';
import nlAuth from '@/locales/nl/auth.json';
import nlCommon from '@/locales/nl/common.json';
import nlPages from '@/locales/nl/pages.json';
import nlComponents from '@/locales/nl/components.json';

export type LanguageCode = 'en' | 'de' | 'da' | 'no' | 'sv' | 'nl';

const defaultLanguage : LanguageCode = 'en';

const resources : Record<LanguageCode, { translation: any }> = {
  en: {
    translation: {
      ...auth,
      ...common,
      ...pages,
      ...components
    }
  },
  de: {
    translation: {
      ...deAuth,
      ...deCommon,
      ...dePages,
      ...deComponents
    }
  },
  da: {
    translation: {
      ...daAuth,
      ...daCommon,
      ...daPages,
      ...daComponents
    }
  },
  no: {
    translation: {
      ...noAuth,
      ...noCommon,
      ...noPages,
      ...noComponents
    }
  },
  sv: {
    translation: {
      ...svAuth,
      ...svCommon,
      ...svPages,
      ...svComponents
    }
  },
  nl: {
    translation: {
      ...nlAuth,
      ...nlCommon,
      ...nlPages,
      ...nlComponents
    }
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: defaultLanguage,
    fallbackLng: defaultLanguage,
    interpolation: {
      escapeValue: false, // React already escapes
    },
  });

export default i18n;
