import React, { memo } from "react";
import MembersCard, { MembersCardSkeleton } from "../memberscard/MembersCard";
import { ModelInterface } from "@/types";
import { Col, Row, Spinner } from "react-bootstrap";
import { useInfiniteScroll } from "@/hooks";

interface InfiniteMembersListProps {
  models: ModelInterface[];
  hasNextPage: boolean;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  refetchModels: () => void;
}

const InfiniteMembersList: React.FC<InfiniteMembersListProps> = memo(
  ({
    models,
    hasNextPage,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    refetchModels,
  }) => {
    const { loadMoreRef } = useInfiniteScroll({
      hasNextPage,
      isFetchingNextPage,
      fetchNextPage,
      rootMargin: "200px",
    });

    if (isLoading && models.length === 0) {
      return (
        <Row className="g-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <Col key={`skeleton-${index}`} xs={12} sm={6} md={4} lg={3} xl={2}>
              <MembersCardSkeleton />
            </Col>
          ))}
        </Row>
      );
    }

    return (
      <>
        <div className="members-list-content">
          {models.map((model: ModelInterface) => (
            <MembersCard
              key={model.id}
              {...model}
              refetchModels={refetchModels}
            />
          ))}
        </div>

        <div ref={loadMoreRef} style={{ height: "20px" }} />

        {isFetchingNextPage && (
          <div className="d-flex justify-content-center align-items-center w-100 py-4">
            <Spinner animation="border" />
          </div>
        )}
      </>
    );
  }
);

InfiniteMembersList.displayName = "InfiniteMembersList";

export default InfiniteMembersList;
