import { useEffect, useRef, useState } from "react";
import { loadGoogleMapsAPI } from "@/utils/googleMaps";
import "./styles.scss";

interface CityOption {
  value: string;
  label: string;
  place_id: string;
}

interface CityAutocompleteProps {
  onPlaceSelect: (place: google.maps.places.PlaceResult | null) => void;
  countryCode?: string;
  disabled?: boolean;
  placeholder?: string;
  value?: string;
  className?: string;
  isInvalid?: boolean;
}

const CityAutocomplete = ({
  onPlaceSelect,
  countryCode,
  disabled = false,
  placeholder,
  value,
  isInvalid = false,
}: CityAutocompleteProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(value || "");
  const [options, setOptions] = useState<CityOption[]>([]);
  const [selectedOption, setSelectedOption] = useState<CityOption | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  useEffect(() => {
    const initializeServices = async () => {
      try {
        await loadGoogleMapsAPI();
        if (window.google && window.google.maps && window.google.maps.places) {
          autocompleteService.current = new window.google.maps.places.AutocompleteService();
          const dummyDiv = document.createElement('div');
          placesService.current = new window.google.maps.places.PlacesService(dummyDiv);
        }
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
      }
    };

    initializeServices();
  }, []);

  const searchCities = async (query: string) => {
    if (!autocompleteService.current || !query.trim()) {
      setOptions([]);
      return;
    }

    setIsLoading(true);

    const request: google.maps.places.AutocompletionRequest = {
      input: query,
      types: ['(cities)'],
    };

    if (countryCode) {
      request.componentRestrictions = { country: countryCode.toLowerCase() };
    }

    try {
      autocompleteService.current.getPlacePredictions(request, (predictions, status) => {
        setIsLoading(false);

        if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
          const cityOptions: CityOption[] = predictions.map(prediction => ({
            value: prediction.place_id,
            label: prediction.description,
            place_id: prediction.place_id
          }));
          setOptions(cityOptions);
        } else {
          setOptions([]);
        }
      });
    } catch (error) {
      console.error('Error fetching city predictions:', error);
      setIsLoading(false);
      setOptions([]);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm && isOpen) {
        searchCities(searchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, countryCode, isOpen]);

  const selectOption = async (option: CityOption) => {
    if (!placesService.current) return;

    setSelectedOption(option);
    setSearchTerm(option.label);
    setIsOpen(false);
    setFocusedIndex(-1);

    const request: google.maps.places.PlaceDetailsRequest = {
      placeId: option.place_id,
      fields: ['geometry', 'name', 'formatted_address', 'place_id']
    };

    placesService.current.getDetails(request, (place, status) => {
      if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {
        onPlaceSelect(place);
      } else {
        onPlaceSelect(null);
      }
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        setIsOpen(true);
        return;
      }
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev =>
          prev < options.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => prev > 0 ? prev - 1 : prev);
        break;
      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && options[focusedIndex]) {
          selectOption(options[focusedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setFocusedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    setSelectedOption(null);

    if (newValue.trim()) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
      setOptions([]);
      onPlaceSelect(null);
    }
  };

  const handleFocus = () => {
    if (searchTerm.trim()) {
      setIsOpen(true);
    }
  };

  const handleBlur = (e: React.FocusEvent) => {
    setTimeout(() => {
      if (!containerRef.current?.contains(e.relatedTarget as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    }, 150);
  };

  const handleOptionClick = (option: CityOption) => {
    selectOption(option);
  };

  useEffect(() => {
    if (value !== undefined && value !== searchTerm) {
      setSearchTerm(value);
    }
  }, [value]);

  return (
    <div
      ref={containerRef}
      className={`place-autocomplete-container ${isInvalid ? 'is-invalid' : ''}`}
      style={{ position: 'relative' }}
    >
      <div className={`select__control ${isOpen ? 'select__control--is-focused' : ''} ${disabled ? 'select__control--is-disabled' : ''}`}>
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          placeholder={placeholder || (countryCode ? "Select a city" : "Select a country first")}
          className="select__input"
          style={{
            border: 'none',
            outline: 'none',
            background: 'transparent',
            width: '100%',
            fontSize: 'inherit',
            color: 'inherit'
          }}
        />
        <div className="select__indicators">
          {isLoading && (
            <div className="select__loading-indicator">
              <span>...</span>
            </div>
          )}
          <div className="select__dropdown-indicator">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
              <path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"></path>
            </svg>
          </div>
        </div>
      </div>

      {isOpen && options.length > 0 && (
        <div className="select__menu">
          <div className="select__menu-list">
            {options.map((option, index) => (
              <div
                key={option.place_id}
                className={`select__option ${
                  index === focusedIndex ? 'select__option--is-focused' : ''
                } ${
                  selectedOption?.place_id === option.place_id ? 'select__option--is-selected' : ''
                }`}
                onClick={() => handleOptionClick(option)}
                onMouseEnter={() => setFocusedIndex(index)}
              >
                {option.label}
              </div>
            ))}
          </div>
        </div>
      )}

      {isOpen && searchTerm && !isLoading && options.length === 0 && (
        <div className="select__menu">
          <div className="select__menu-list">
            <div className="select__option select__option--no-options">
              No cities found
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CityAutocomplete;
