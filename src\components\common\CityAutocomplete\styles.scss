@use "@/variables" as *;

.place-autocomplete-container {
  position: relative;

  .select__control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    color: $text-color;
    padding: 16px 30px;
    border-radius: 8px;
    border: 1px solid $light-gray-color;
    background: $white-color;
    box-shadow: none;
    max-height: 62px;
    cursor: pointer;

    .select__input {
      flex: 1;
    }

    .select__indicators {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .select__dropdown-indicator {
      color: $link-color;
      transition: transform 0.2s;
    }

    &--is-focused {
      border-color: $primary-color;
      box-shadow: 0 0 0 1px $primary-color;

      .select__dropdown-indicator {
        transform: rotate(180deg);
      }
    }

    &--is-disabled {
      background-color: $light-gray-color;
      cursor: not-allowed;
    }

    .select__loading-indicator {
      color: $link-color;
    }
  }

  .select__menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    background-color: $white-color;
    border: 1px solid $light-gray-color;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
    font-size: 15px;
  }

  .select__menu-list {
    padding: 0;
  }

  .select__option {
    padding: 15px 30px;
    cursor: pointer;
    transition: background-color 0.2s;

    &--is-focused {
      background-color: $light-gray-color;
    }

    &--is-selected {
      background-color: $primary-color;
      color: $white-color;
    }

    &--no-options {
      color: $link-color;
      cursor: default;
    }

    &:hover {
      background-color: $light-gray-color;
    }
  }

  &.is-invalid .select__control {
    border-color: #dc3545;

    &--is-focused {
      border-color: #dc3545;
      box-shadow: 0 0 0 1px #dc3545;
    }
  }
}
