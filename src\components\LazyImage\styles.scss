.image-skeleton {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f0f0f0;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  z-index: 1;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -150%;
    height: 100%;
    width: 150%;
    background: linear-gradient(
      90deg,
      rgba(240, 240, 240, 0) 0%,
      rgba(255, 255, 255, 0.6) 50%,
      rgba(240, 240, 240, 0) 100%
    );
    animation: shimmer .5s infinite;
  }

  svg {
    opacity: 0.6;
    z-index: 2;
  }
}

@keyframes shimmer {
  0% {
    left: -150%;
  }
  100% {
    left: 100%;
  }
}
