{"home": {"title": "Find a chat partner", "description": "Discover the best chat partner.", "noMatchesTitle": "No Matches Found", "noMatchesDescription": "Sorry, we couldn't find any matches for you.", "tryAdjustingFilters": "Please try adjusting your filters."}, "messages": {"back": "Back", "searchPeople": "Search people", "enterMessage": "Enter Message"}, "flirts": {"received": "Received", "sent": "<PERSON><PERSON>"}, "viewedMe": {"myViews": "My Views ({{count}})", "viewedMe": "Viewed Me ({{count}})"}, "myProfile": {"title": "My Profile", "viewProfile": "View Profile", "myPhotos": "My Photos", "changePassword": "Change Password", "settings": "Settings", "logout": "Logout", "submit": "Submit", "cancel": "Cancel", "profileUpdatedSuccess": "Profile updated successfully!"}, "coin": {"title": "Order Your Discounted Coins Now!", "description": "", "failedToLoad": "Failed to load packages.", "purchaseSuccess": "Purchase successful!", "purchaseFailed": "Purchase failed."}, "notFound": {"title": "Oops! Page Not Found", "description": "Sorry, the page you are looking for doesn't exist. <br/> Maybe you took a wrong turn on your search for love! <br/> Let's get you back to where the magic happens.", "goHome": "Go Back to Home"}, "profileManagement": {"title": "Profile Management", "submit": "Submit", "cancel": "Cancel", "accordion": {"basicInfo": "Basic Information", "appearance": "Appearance", "lifestyle": "Lifestyle", "personalAttributes": "Personal Attributes", "location": "Location", "family": "Family", "aboutMe": "About Me", "profilePicture": "Profile Picture"}, "fields": {"name": "Your Name", "namePlaceholder": "Enter your name", "dob": "Date of birth", "dobPlaceholder": "Select date of birth", "username": "Username", "usernamePlaceholder": "Enter your username", "seekingFor": "Seeking For", "gender": "Gender", "appearance": "Appearance", "appearancePlaceholder": "Select Appearance", "hairColor": "Hair color", "hairColorPlaceholder": "Select Hair color", "eyeColor": "Eye color", "eyeColorPlaceholder": "Select Eye color", "bodyType": "Body type", "bodyTypePlaceholder": "Select Body type", "bestFeature": "Best Feature", "bestFeaturePlaceholder": "Select Best Feature", "bodyArt": "Body Art", "bodyArtPlaceholder": "Select Body Art", "relationshipStatus": "Relationship Status", "relationshipStatusPlaceholder": "Select Relationship Status", "starSign": "Star Sign", "starSignPlaceholder": "Select Star Sign", "smokingHabit": "Smoking Habit", "smokingHabitPlaceholder": "Select Smoking Habit", "drinkingHabit": "Drinking Habit", "drinkingHabitPlaceholder": "Select Drinking Habit", "sexualOrientation": "Sexual Orientation", "sexualOrientationPlaceholder": "Select Sexual Orientation", "personality": "Personality", "personalityPlaceholder": "Select Personality", "ethnicity": "Ethnicity", "ethnicityPlaceholder": "Select Ethnicity", "religion": "Religion", "religionPlaceholder": "Select Religion", "interest": "Interest", "interestPlaceholder": "Select Interest", "city": "City", "cityPlaceholder": "Enter City", "kids": "Kids", "kidsPlaceholder": "Enter Kids", "aboutMe": "About Me", "aboutMePlaceholder": "Enter About Me", "height": "Height", "heightPlaceholder": "Enter Height", "weight": "Weight", "weightPlaceholder": "Enter Weight"}}}