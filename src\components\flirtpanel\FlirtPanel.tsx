import { useFlirtMessages } from "@/api";
import useChatStore from "@/stores/useChatStore";
import { FormatLocation } from "@/utils";
import React, { useEffect } from "react";
import { Off<PERSON><PERSON>, Spinner } from "react-bootstrap";
import "./styles.scss";

interface FlirtMessage {
  id: number;
  message: string;
  languageCode: string;
}

const FlirtPanel: React.FC<any> = ({
  show,
  onHide,
  memberId,
  modelInfo,
}: any) => {
  const { data: { data: { flirtMessage = [] } = {} } = {}, isLoading } =
    useFlirtMessages();
  const { setActiveChatUser, sendMessage, isConnected, getChatMessages } =
    useChatStore();
  // const { mutateAsync: sendFlirt } = useSendFlirtMutation();

  const handleClose = () => {
    onHide();
  };

  useEffect(() => {
    if (modelInfo?.id && isConnected) {
      getChatMessages(modelInfo.id);
    }
  }, [modelInfo?.id, isConnected, getChatMessages]);

  const handleSendFlirt = async (item: FlirtMessage) => {
    sendMessage(modelInfo?.id, item.message);
    setActiveChatUser({
      id: modelInfo?.id,
      name: modelInfo?.username,
      location: FormatLocation(undefined, modelInfo?.city),
      avatar: modelInfo?.avatar,
    });
    handleClose()
    // try {
    //   const payload = {
    //     model_id: memberId,
    //     flirtMessageId: item.id,
    //   };
    //   const response: any = await sendFlirt(payload);
    //   if (response?.success) {
    //     toast.success(response?.message);
    //   }
    // } catch (err) {
    //   console.log(err);
    // }
  };

  return (
    <>
      <Offcanvas
        show={show}
        onHide={handleClose}
        placement="end"
        className="flirt-offcanvas"
      >
        <Offcanvas.Header closeButton>
          <Offcanvas.Title>Flirt with Me</Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body className="d-flex flex-column gap-3">
          {isLoading ? (
            <div className="d-flex justify-content-center align-items-center h-100">
              <Spinner />
            </div>
          ) : (
            <>
              {flirtMessage.map((msg: FlirtMessage) => (
                <div
                  key={msg?.id}
                  className="flirt-message rounded-3 p-3 cursor-pointer"
                  onClick={() => handleSendFlirt(msg)}
                >
                  {msg?.message}
                </div>
              ))}
            </>
          )}
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};

export default FlirtPanel;
