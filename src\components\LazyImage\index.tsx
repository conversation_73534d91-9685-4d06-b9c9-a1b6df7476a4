import defaultProfile from "@/assets/images/user.png";
import React, { useState } from "react";
import { Image } from "react-bootstrap";
import { Image as ImageIcon } from "iconsax-react";
import "./styles.scss";

interface LazyImageProps {
  src?: string;
  alt?: string;
  className?: string;
}

const LazyImage: React.FC<LazyImageProps> = ({ src, alt = "", className }) => {
  const [loaded, setLoaded] = useState(false);

  return (
    <div className={`lazy-image-wrapper position-relative ${className}`}>
      {!loaded && (
        <div className="image-skeleton position-absolute top-0 start-0 w-100 h-100">
          <ImageIcon size="32" color="#B936AD" />
        </div>
      )}
      <Image
        src={src || defaultProfile}
        alt={alt}
        className={`member-img w-100 ${loaded ? "visible" : "invisible"}`}
        onLoad={() => setTimeout(() => setLoaded(true), 500)}
        loading="lazy"
      />
    </div>
  );
};

export default LazyImage;
