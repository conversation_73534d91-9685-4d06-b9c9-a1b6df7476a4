// Google Maps API loader utility
let isLoaded = false;
let isLoading = false;
const loadPromises: Promise<void>[] = [];

export const loadGoogleMapsAPI = (): Promise<void> => {
  if (isLoaded) {
    return Promise.resolve();
  }

  if (isLoading) {
    return loadPromises[loadPromises.length - 1];
  }

  isLoading = true;
  
  const promise = new Promise<void>((resolve, reject) => {
    const apiKey = import.meta.env.VITE_GOOGLE_MAP_API_KEY;
    
    if (!apiKey) {
      reject(new Error('Google Maps API key not found'));
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      isLoaded = true;
      isLoading = false;
      resolve();
    };
    
    script.onerror = () => {
      isLoading = false;
      reject(new Error('Failed to load Google Maps API'));
    };
    
    document.head.appendChild(script);
  });

  loadPromises.push(promise);
  return promise;
};
