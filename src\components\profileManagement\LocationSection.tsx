import React from "react";
import { Accordion, Form } from "react-bootstrap";
import { CityAutocomplete } from "@/components/common";
import { useCountries } from "@/api/utils.api";

interface LocationSectionProps {
  formik: any;
  handleBlurUncontrolled?: (field: string) => (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const LocationSection: React.FC<LocationSectionProps> = ({ formik }) => {
  const { data: countries = [] } = useCountries();

  const userCountry = countries.find((c: any) => c.id === formik.values.countryId);

  return (
    <Accordion.Item eventKey="4">
      <Accordion.Header>Location</Accordion.Header>
      <Accordion.Body>
        <div className="form-input-group d-flex flex-wrap">
          <Form.Group className="form-input">
            <Form.Label>Country</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter Country"
              name="country"
              defaultValue={userCountry?.name}
              disabled
              className="opacity-75"
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>City</Form.Label>
            <CityAutocomplete
              onPlaceSelect={(place: google.maps.places.PlaceResult | null) => {
                formik.setFieldValue("city", place?.formatted_address || "");
              }}
              countryCode={userCountry?.code}
              placeholder="Enter City"
              value={formik.values.city}
            />
          </Form.Group>
        </div>
      </Accordion.Body>
    </Accordion.Item>
  );
};

export default LocationSection;