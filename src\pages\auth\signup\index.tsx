import { useRequestOTPMutation } from "@/api/auth.api";
import { useCountries, useMaster } from "@/api/utils.api";
import { DOBDropdown, CityAutocomplete } from "@/components/common";
import PasswordField from "@/components/common/PasswordField";
import { SignupSchemaValidation } from "@/formSchema/schemaValidations";
import { useTranslation } from "@/hooks/useTranslation";
import { ROUTE_PATH } from "@/routes";
import useAuthStore from "@/stores/auth";
import { SignupInterface } from "@/types";
import { getAppOriginURL } from "@/utils";

import { useFormik } from "formik";
import parse from "html-react-parser";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { Link, useNavigate } from "react-router-dom";
import ReactSelect from "react-select";

const Signup = () => {
  const { t } = useTranslation();
  const { data: countries = [], isLoading: isCountriesLoading } = useCountries();
  const { data: master = {}, isLoading: isMasterLoading } = useMaster();
  const { mutateAsync: requestOtp } = useRequestOTPMutation();
  const setSignupUserInfo = useAuthStore((state) => state.setSignupUserInfo);
  const navigate = useNavigate();

  type Country = { id: number; name: string; code: string };
  type OptionType = { value: number; label: string };

  const seekingForOptions = (master.seeking_for || []).map((item: any) => ({
    value: item.id,
    label: item.title,
  }));

  const genderOptions = (master.gender || []).map((item: any) => ({
    value: item.id,
    label: item.title,
  }));

  const formik = useFormik<
    SignupInterface & {
      confirmPassword: string;
      agreeTerms: boolean;
      seekingFor: number | undefined;
      gender: number | undefined;
      city: google.maps.places.PlaceResult | null;
    }
  >({
    initialValues: {
      seekingFor: undefined,
      gender: undefined,
      username: "",
      email: "",
      dob: "",
      countryId: undefined,
      password: "",
      confirmPassword: "",
      agreeTerms: false,
      city: null,
    },
    validationSchema: SignupSchemaValidation,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const trimmedValues = {
          ...values,
          username: values.username.trim(),
          email: values.email.trim(),
          city: values.city?.formatted_address || "",
        };

        const signupPayload = {
          seekingFor: trimmedValues.seekingFor,
          gender: trimmedValues.gender,
          username: trimmedValues.username,
          email: trimmedValues.email,
          dob: trimmedValues.dob,
          countryId: trimmedValues.countryId,
          city: trimmedValues.city,
          password: trimmedValues.password,
        };

        const result: any = await requestOtp({
          type: "SIGN_UP",
          email: trimmedValues.email,
          username: trimmedValues.username,
        });
        if (result?.success) {
          toast.success(result?.message);
          setSignupUserInfo(signupPayload);
          navigate(
            `${ROUTE_PATH.VERIFYACCOUNT}?type=SIGN_UP&email=${values.email}`
          );
        }
      } catch (error) {
        console.error("Signup error:", error);
        toast.error(t("signup.error"));
      } finally {
        setSubmitting(false);
      }
    },
  });

  const {
    handleChange,
    handleBlur,
    handleSubmit,
    values,
    touched,
    errors,
    setFieldValue,
    isSubmitting,
  } = formik;

  const selectedCountry = countries.find((c: Country) => c.id === values.countryId);

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4">
        <div className="auth-form-heading text-center">
          <h3 className="mb-2 mb-sm-3">{parse(t("signup.heading"))}</h3>
        </div>
        <Form noValidate onSubmit={handleSubmit}>
            <div className="form-input-group d-flex flex-column">
              <Form.Group className="form-input">
                <Form.Label>{t("signup.gender")}</Form.Label>
                <ReactSelect
                  options={genderOptions}
                  isSearchable={false}
                  name="gender"
                  value={
                    genderOptions.find(
                      (option: any) => option.value === values.gender
                    ) || null
                  }
                  onChange={(option: any) =>
                    setFieldValue("gender", option?.value || 0)
                  }
                  placeholder={
                    isMasterLoading ? t("common.loading") : t("common.select")
                  }
                  classNamePrefix="select"
                />
                {touched.gender && errors.gender && (
                  <small className="text-danger">{errors.gender as string}</small>
                )}
              </Form.Group>

              <Form.Group className="form-input">
                <Form.Label>{t("signup.seekingFor")}</Form.Label>
                <ReactSelect
                  options={seekingForOptions}
                  isSearchable={false}
                  name="seekingFor"
                  value={
                    seekingForOptions.find(
                      (option: any) => option.value === values.seekingFor
                    ) || null
                  }
                  onChange={(option: any) =>
                    setFieldValue("seekingFor", option?.value || 0)
                  }
                  placeholder={
                    isMasterLoading ? t("common.loading") : t("common.select")
                  }
                  classNamePrefix="select"
                />
                {touched.seekingFor && errors.seekingFor && (
                  <small className="text-danger">{errors.seekingFor as string}</small>
                )}
              </Form.Group>

              <Form.Group className="form-input">
                <Form.Label>{t("signup.username")}</Form.Label>
                <Form.Control
                  name="username"
                  type="text"
                  placeholder={t("signup.usernamePlaceholder")}
                  value={values.username}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isInvalid={!!touched.username && !!errors.username}
                />
                <Form.Control.Feedback type="invalid" className="text-danger">
                  {errors.username}
                </Form.Control.Feedback>
              </Form.Group>

              <Form.Group className="form-input">
                <Form.Label>{t("signup.email")}</Form.Label>
                <Form.Control
                  name="email"
                  type="email"
                  placeholder={t("signup.emailPlaceholder")}
                  value={values.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isInvalid={!!touched.email && !!errors.email}
                />
                <Form.Control.Feedback type="invalid" className="text-danger">
                  {errors.email}
                </Form.Control.Feedback>
              </Form.Group>

              <Form.Group className="form-input">
                <Form.Label>{t("signup.dob")}</Form.Label>
                <DOBDropdown
                  value={values.dob}
                  onChange={(date) => setFieldValue("dob", date)}
                  onBlur={handleBlur}
                  error={errors.dob}
                  touched={touched.dob}
                  maxAge={18}
                />
              </Form.Group>

              <Form.Group className="form-input">
                <Form.Label>{t("signup.country")}</Form.Label>
                <ReactSelect
                  options={countries.map((c: Country) => ({
                    value: c.id,
                    label: c.name,
                    code: c.code,
                  }))}
                  isSearchable={true}
                  name="countryId"
                  value={
                    countries
                      .map((c: Country) => ({
                        value: c.id,
                        label: c.name,
                        code: c.code,
                      }))
                      .find(
                        (option: OptionType) => option.value === values.countryId
                      ) || null
                  }
                  onChange={(option: OptionType | null) => {
                    setFieldValue("countryId", option?.value || 0);
                    setFieldValue("city", null);
                  }}
                  placeholder={
                    isCountriesLoading
                      ? t("common.loading")
                      : t("signup.countryPlaceholder")
                  }
                  classNamePrefix="select"
                />
                {touched.countryId && errors.countryId && (
                  <small className="text-danger">{errors.countryId as string}</small>
                )}
              </Form.Group>

              <Form.Group className="form-input">
                <Form.Label>{t("signup.city")}</Form.Label>
                <CityAutocomplete
                  onPlaceSelect={(place: google.maps.places.PlaceResult | null) => setFieldValue("city", place)}
                  countryCode={selectedCountry?.code}
                  disabled={!values.countryId}
                  placeholder={t("signup.cityPlaceholder")}
                  isInvalid={!!touched.city && !!errors.city}
                />
                {touched.city && errors.city && (
                  <div className="text-danger">{errors.city as string}</div>
                )}
              </Form.Group>

              <PasswordField
                label={t("signup.password")}
                name="password"
                placeholder={t("signup.passwordPlaceholder")}
                values={values}
                handleChange={handleChange}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
              />

              <PasswordField
                label={t("signup.confirmPassword")}
                name="confirmPassword"
                placeholder={t("signup.confirmPasswordPlaceholder")}
                values={values}
                handleChange={handleChange}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
              />

              <Form.Group className="form-check mt-2">
                <Form.Check type="checkbox" id={`check-api-checkbox`}>
                  <Form.Check.Input
                    type="checkbox"
                    name="agreeTerms"
                    checked={values.agreeTerms}
                    onChange={handleChange}
                    isInvalid={!!touched.agreeTerms && !!errors.agreeTerms}
                  />
                  <Form.Check.Label>
                    {parse(
                      t("signup.termsAndConditions", {
                        "t&c_url": `${getAppOriginURL()}${ROUTE_PATH.TERMS_CONDITIONS}`,
                        "p&c_url": `${getAppOriginURL()}${ROUTE_PATH.PRIVACY_POLICY}`,
                      })
                    )}
                  </Form.Check.Label>
                </Form.Check>

                <small className="text-danger">
                  {touched.agreeTerms && errors.agreeTerms}
                </small>
              </Form.Group>
            </div>

            <Button
              type="submit"
              className="w-100 mt-3 mt-sm-4"
              disabled={isSubmitting}
            >
              {isSubmitting ? t("signup.registering") : t("signup.submit")}
            </Button>
          </Form>
      </div>

      <div className="text-center mt-3 mt-sm-4">
        <p className="mb-0">
          {t("signup.haveAccount")}{" "}
          <Link to={ROUTE_PATH.LOGIN}>{t("signup.signin")}</Link>
        </p>
      </div>
    </div>
  );
};

export default Signup;